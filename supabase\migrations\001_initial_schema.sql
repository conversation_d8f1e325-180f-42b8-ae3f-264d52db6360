-- 智能书签管家 MVP 数据库表结构
-- 创建时间: 2025-01-31

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户信息表 (profiles)
-- 扩展 Supabase Auth 的用户信息
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR NOT NULL,
  display_name VARCHAR,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 用户工作空间表 (user_bookmark_workspace)
-- 每个用户只有一个工作空间，简化数据模型
CREATE TABLE user_bookmark_workspace (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE UNIQUE,
  status VARCHAR DEFAULT 'idle' CHECK (status IN ('idle', 'processing', 'completed', 'failed')),
  original_file_key VARCHAR, -- R2 存储的原始文件键
  result_file_key VARCHAR,   -- R2 存储的结果文件键
  original_filename VARCHAR,
  browser_type VARCHAR,
  total_bookmarks INTEGER DEFAULT 0,
  processed_bookmarks INTEGER DEFAULT 0,
  ai_learning_examples JSONB DEFAULT '[]'::jsonb, -- AI 学习示例
  processing_metadata JSONB DEFAULT '{}'::jsonb,  -- 处理元数据
  last_import_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 用户分类表 (user_categories)
-- 用户自定义的书签分类
CREATE TABLE user_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  type VARCHAR DEFAULT 'manual' CHECK (type IN ('manual', 'ai', 'alphabetical')),
  color VARCHAR, -- 十六进制颜色代码
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, name) -- 同一用户的分类名称不能重复
);

-- 书签处理缓存表 (bookmark_processing_cache)
-- 临时存储正在处理的书签数据，处理完成后清空
CREATE TABLE bookmark_processing_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID NOT NULL REFERENCES user_bookmark_workspace(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  url TEXT NOT NULL,
  original_path TEXT, -- 原始文件夹路径
  status VARCHAR DEFAULT 'unprocessed' CHECK (status IN ('unprocessed', 'confirmed', 'rejected')),
  ai_suggestion JSONB, -- AI 分类建议 {category, confidence, reasons}
  suggested_category VARCHAR,
  final_category VARCHAR,
  classification_type VARCHAR DEFAULT 'manual' CHECK (classification_type IN ('manual', 'ai', 'alphabetical')),
  is_duplicate BOOLEAN DEFAULT FALSE,
  duplicate_url TEXT, -- 如果是重复书签，指向原始URL
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_workspace_user_id ON user_bookmark_workspace(user_id);
CREATE INDEX idx_workspace_status ON user_bookmark_workspace(status);
CREATE INDEX idx_categories_user_id ON user_categories(user_id);
CREATE INDEX idx_categories_type ON user_categories(type);
CREATE INDEX idx_bookmark_cache_workspace_id ON bookmark_processing_cache(workspace_id);
CREATE INDEX idx_bookmark_cache_status ON bookmark_processing_cache(status);
CREATE INDEX idx_bookmark_cache_url ON bookmark_processing_cache(url);
CREATE INDEX idx_bookmark_cache_final_category ON bookmark_processing_cache(final_category);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workspace_updated_at 
    BEFORE UPDATE ON user_bookmark_workspace 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at 
    BEFORE UPDATE ON user_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
