// Supabase 数据库类型定义
// 这个文件应该与数据库结构保持同步

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          display_name: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          display_name?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      user_bookmark_workspace: {
        Row: {
          id: string
          user_id: string
          status: string
          original_file_key: string | null
          result_file_key: string | null
          original_filename: string | null
          browser_type: string | null
          total_bookmarks: number
          processed_bookmarks: number
          ai_learning_examples: Json
          processing_metadata: Json
          last_import_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          status?: string
          original_file_key?: string | null
          result_file_key?: string | null
          original_filename?: string | null
          browser_type?: string | null
          total_bookmarks?: number
          processed_bookmarks?: number
          ai_learning_examples?: Json
          processing_metadata?: Json
          last_import_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          status?: string
          original_file_key?: string | null
          result_file_key?: string | null
          original_filename?: string | null
          browser_type?: string | null
          total_bookmarks?: number
          processed_bookmarks?: number
          ai_learning_examples?: Json
          processing_metadata?: Json
          last_import_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_bookmark_workspace_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      user_categories: {
        Row: {
          id: string
          user_id: string
          name: string
          type: string
          color: string | null
          description: string | null
          sort_order: number
          is_default: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type?: string
          color?: string | null
          description?: string | null
          sort_order?: number
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: string
          color?: string | null
          description?: string | null
          sort_order?: number
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_categories_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      bookmark_processing_cache: {
        Row: {
          id: string
          workspace_id: string
          title: string
          url: string
          original_path: string | null
          status: string
          ai_suggestion: Json | null
          suggested_category: string | null
          final_category: string | null
          classification_type: string
          is_duplicate: boolean
          duplicate_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          workspace_id: string
          title: string
          url: string
          original_path?: string | null
          status?: string
          ai_suggestion?: Json | null
          suggested_category?: string | null
          final_category?: string | null
          classification_type?: string
          is_duplicate?: boolean
          duplicate_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          workspace_id?: string
          title?: string
          url?: string
          original_path?: string | null
          status?: string
          ai_suggestion?: Json | null
          suggested_category?: string | null
          final_category?: string | null
          classification_type?: string
          is_duplicate?: boolean
          duplicate_url?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookmark_processing_cache_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "user_bookmark_workspace"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      user_workspace_overview: {
        Row: {
          user_id: string | null
          email: string | null
          display_name: string | null
          workspace_id: string | null
          status: string | null
          total_bookmarks: number | null
          processed_bookmarks: number | null
          last_import_at: string | null
          custom_categories_count: number | null
          pending_bookmarks_count: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      get_user_workspace_info: {
        Args: {
          user_uuid: string
        }
        Returns: {
          workspace_id: string
          status: string
          total_bookmarks: number
          processed_bookmarks: number
          categories_count: number
          last_import_at: string | null
        }[]
      }
      clear_user_bookmark_cache: {
        Args: {
          user_uuid: string
        }
        Returns: boolean
      }
      batch_insert_bookmarks: {
        Args: {
          workspace_uuid: string
          bookmarks_data: Json
        }
        Returns: number
      }
      get_bookmark_stats: {
        Args: {
          workspace_uuid: string
        }
        Returns: {
          total_count: number
          unprocessed_count: number
          confirmed_count: number
          rejected_count: number
          duplicate_count: number
        }[]
      }
      detect_duplicate_bookmarks: {
        Args: {
          workspace_uuid: string
        }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
