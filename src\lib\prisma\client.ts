import { PrismaClient } from '@prisma/client';

// 全局 Prisma 客户端实例
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// 类型导出
export type { 
  Profile,
  UserBookmarkWorkspace,
  UserCategory,
  BookmarkProcessingCache
} from '@prisma/client';
