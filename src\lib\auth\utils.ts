import { AuthError } from '@supabase/supabase-js';

/**
 * 认证工具函数
 */

/**
 * 格式化认证错误消息
 */
export function formatAuthError(error: AuthError | null): string {
  if (!error) return '';

  switch (error.message) {
    case 'Invalid login credentials':
      return '邮箱或密码错误，请检查后重试';
    case 'Email not confirmed':
      return '请先确认您的邮箱地址';
    case 'User already registered':
      return '该邮箱已被注册，请直接登录';
    case 'Password should be at least 6 characters':
      return '密码至少需要6个字符';
    case 'Unable to validate email address: invalid format':
      return '邮箱格式不正确';
    case 'Signup is disabled':
      return '注册功能暂时关闭';
    case 'Email rate limit exceeded':
      return '邮件发送过于频繁，请稍后再试';
    case 'Invalid email or password':
      return '邮箱或密码错误';
    case 'Too many requests':
      return '请求过于频繁，请稍后再试';
    default:
      // 记录未知错误以便调试
      console.error('Unknown auth error:', error);
      return '认证失败，请稍后重试';
  }
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证密码强度
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (password.length < 6) {
    errors.push('密码至少需要6个字符');
  }

  if (password.length > 128) {
    errors.push('密码不能超过128个字符');
  }

  if (!/[a-zA-Z]/.test(password)) {
    errors.push('密码需要包含至少一个字母');
  }

  if (!/\d/.test(password)) {
    errors.push('密码需要包含至少一个数字');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 生成安全的重定向URL
 */
export function getSafeRedirectUrl(redirect: string | null, fallback = '/dashboard'): string {
  if (!redirect) return fallback;

  // 确保重定向URL是相对路径，防止开放重定向攻击
  try {
    const url = new URL(redirect, 'http://localhost');
    if (url.pathname.startsWith('/')) {
      return url.pathname + url.search;
    }
  } catch {
    // 如果URL解析失败，使用fallback
  }

  return fallback;
}

/**
 * 检查是否是强密码
 */
export function isStrongPassword(password: string): boolean {
  // 至少8个字符，包含大小写字母、数字和特殊字符
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return strongPasswordRegex.test(password);
}

/**
 * 获取密码强度等级
 */
export function getPasswordStrength(password: string): {
  level: 'weak' | 'medium' | 'strong';
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('至少需要8个字符');
  }

  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('需要包含小写字母');
  }

  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('需要包含大写字母');
  }

  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('需要包含数字');
  }

  if (/[@$!%*?&]/.test(password)) {
    score += 1;
  } else {
    feedback.push('建议包含特殊字符');
  }

  let level: 'weak' | 'medium' | 'strong';
  if (score <= 2) {
    level = 'weak';
  } else if (score <= 4) {
    level = 'medium';
  } else {
    level = 'strong';
  }

  return { level, score, feedback };
}
