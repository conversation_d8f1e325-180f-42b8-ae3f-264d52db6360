# supabase

```
project_name=bookmark-manager
password=Jv_?$T.NUqc2PgM
Direct connection=postgresql://postgres:Jv_?$<EMAIL>:5432/postgres
Transaction pooler=postgresql://postgres.asxlymieouubswoagcdh:Jv_?$<EMAIL>:6543/postgres
Session pooler=postgresql://postgres.asxlymieouubswoagcdh:Jv_?$<EMAIL>:5432/postgres
NEXT_PUBLIC_SUPABASE_URL=https://asxlymieouubswoagcdh.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFzeGx5bWllb3V1YnN3b2FnY2RoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NDIxMjcsImV4cCI6MjA2OTUxODEyN30.ApXqJ8JwNUEeA6_rKyyb_5UlWtCiWVorCyq7l6SgrkA
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFzeGx5bWllb3V1YnN3b2FnY2RoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mzk0MjEyNywiZXhwIjoyMDY5NTE4MTI3fQ.8m8ipB5v_TVBbWdAhoHGQdFxdU4dfSqQJvIQEeYnZLg

# 数据库迁移文件已创建：
- supabase/migrations/001_initial_schema.sql (基础表结构)
- supabase/migrations/002_rls_policies.sql (安全策略)
- supabase/migrations/003_helper_functions.sql (辅助函数)
```

# cloudfare

```
桶=bmmanager
令牌值=C-Biyl5ai7P5mW_6z8soWSXUWq79tGv7qsGOIXOq
访问密钥 ID=bdd242e28c42d03617941c73366c32c9
机密访问密钥=487596d9988dd26f23fa49571e373a8a9dd50023fe148edd2c8b3b65c6d2e96d
为 S3 客户端使用管辖权地特定的终结点=https://e42adfd74794cbebc7d2fc635f1a5edd.r2.cloudflarestorage.com
```
