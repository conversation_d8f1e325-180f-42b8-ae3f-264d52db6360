'use client';

import React from 'react';
import { useAuthRedirect } from '@/hooks/use-auth-redirect';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * 受保护的路由组件
 * 确保只有认证用户才能访问包装的内容
 */
export function ProtectedRoute({ 
  children, 
  fallback = <LoadingSpinner />,
  redirectTo = '/dashboard'
}: ProtectedRouteProps) {
  const { user, loading } = useAuthRedirect({
    requireAuth: true,
    redirectTo,
  });

  if (loading) {
    return <>{fallback}</>;
  }

  if (!user) {
    // 用户未认证，中间件会处理重定向
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * 默认加载组件
 */
function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span className="ml-2 text-gray-600">加载中...</span>
    </div>
  );
}
