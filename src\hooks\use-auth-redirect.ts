'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/auth-context';

interface UseAuthRedirectOptions {
  redirectTo?: string;
  requireAuth?: boolean;
  redirectIfAuthenticated?: boolean;
}

/**
 * 认证重定向 Hook
 * @param options 配置选项
 */
export function useAuthRedirect(options: UseAuthRedirectOptions = {}) {
  const {
    redirectTo = '/dashboard',
    requireAuth = true,
    redirectIfAuthenticated = false,
  } = options;

  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading) return; // 等待认证状态加载完成

    if (requireAuth && !user) {
      // 需要认证但用户未登录，重定向到登录页
      const currentPath = window.location.pathname;
      const loginUrl = `/auth/login?redirect=${encodeURIComponent(currentPath)}`;
      router.push(loginUrl);
    } else if (redirectIfAuthenticated && user) {
      // 用户已登录但访问了不需要认证的页面（如登录页），重定向到指定页面
      router.push(redirectTo);
    }
  }, [user, loading, requireAuth, redirectIfAuthenticated, redirectTo, router]);

  return {
    user,
    loading,
    isAuthenticated: !!user,
  };
}
