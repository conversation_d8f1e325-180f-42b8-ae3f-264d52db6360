# 需求文档：智能书签管家 MVP

## 项目介绍

智能书签管家 MVP 是一个智能化的Web应用程序，旨在帮助用户高效地整理浏览器书签。系统允许用户上传HTML书签文件，通过AI辅助自动分类书签，通过直观的界面手动整理，并导出结构清晰的书签文件。MVP采用7阶段开发方法，AI功能最后实现，前期使用接口占位符。

## 功能需求

### 1. 用户认证系统

**用户故事**：作为用户，我希望能够创建账户并安全登录系统，以便访问我的个人书签整理工作空间。

**验收标准**：
1. 当用户提供有效的邮箱和密码时，系统应创建新账户并发送确认邮件
2. 当用户使用有效凭据尝试登录时，系统应验证用户身份并授予工作空间访问权限
3. 当用户点击"Google登录"时，系统应重定向到Google OAuth并在成功授权后创建/验证账户
4. 当用户请求密码重置时，系统应向其注册邮箱发送安全重置链接
5. 当用户会话过期时，系统应重定向到登录页面并保留其预期目标页面

### 2. 书签文件处理

**用户故事**：作为用户，我希望能够上传浏览器的书签HTML文件，以便系统解析并提取所有书签进行整理。

**验收标准**：
1. 当用户将HTML文件拖拽到上传区域时，系统应接受最大10MB的文件
2. 当用户上传书签文件时，系统应显示上传进度和解析状态
3. 当系统处理书签文件时，应提取每个书签的标题、URL和原始文件夹结构
4. 当解析完成时，系统应将原始文件存储在Cloudflare R2中，书签数据存储在处理缓存中
5. 当解析失败时，系统应显示清晰的错误消息并允许用户重试

### 3. 工作空间管理

**用户故事**：作为用户，我希望拥有一个个人工作空间来管理我的书签整理会话，以便能够在多个会话中持续整理书签。

**验收标准**：
1. 当用户首次登录时，系统应为该用户创建唯一的工作空间
2. 当用户上传新的书签文件时，系统应替换之前的文件并重置工作空间
3. 当用户访问工作空间时，系统应显示当前处理状态和书签数量
4. 当用户点击重置时，系统应清除所有处理数据并返回到原始上传状态
5. 当用户没有活跃工作空间时，系统应显示上传提示

### 4. 书签展示和管理界面

**用户故事**：作为用户，我希望通过直观的界面查看和手动整理书签，以便完全控制我的书签组织方式。

**验收标准**：
1. 当书签加载时，系统应以列表格式显示它们，包含标题、URL和当前分类
2. 当用户搜索书签时，系统应基于标题或URL匹配过滤显示结果
3. 当用户将书签拖拽到分类时，系统应立即更新书签的分类
4. 当用户选择多个书签时，系统应允许批量操作如分类分配
5. 当用户执行任何整理操作时，系统应提供即时的视觉反馈

### 5. 分类调整和优化

**用户故事**：作为用户，我希望能够基于AI初步分类结果调整和优化书签分类，以便创建符合我个人习惯的分类体系。

**验收标准**：
1. 当AI完成初步分类后，用户应能够修改现有分类的名称（如将"技术"改为"前端开发"）
2. 当用户需要新分类时，系统应允许创建自定义分类并设置名称和颜色
3. 当用户删除分类时，系统应将该分类下的所有书签移动到"未分类"或用户指定的其他分类
4. 当用户调整分类时，系统应显示每个分类中的书签数量和预览
5. 当用户完成分类调整时，系统应记录这些偏好用于后续学习和改进

### 6. 分类功能

**用户故事**：作为用户，我希望有多种自动分类书签的方式，以便快速整理大量书签而无需手动操作。

**验收标准**：
1. 当用户启用字母分类时，系统应根据标题自动将书签分类到A-Z类别中
2. 当处理中文书签时，字母分类应使用拼音进行排序
3. 当书签的分类置信度较低时，系统应将它们放入"收件箱"进行手动审核
4. 当系统建议分类时，应显示置信度级别并允许接受/拒绝操作
5. 当用户纠正分类时，系统应将此作为学习示例存储以供未来改进

### 7. AI分类接口（占位符实现）

**用户故事**：作为用户，我希望获得AI驱动的书签分类建议，以便通过智能辅助高效地整理书签。

**验收标准**：
1. 当处理书签时，系统应显示带有置信度分数的分类建议
2. 当用户接受AI建议时，系统应应用分类并记录接受情况
3. 当用户拒绝AI建议时，系统应允许手动分类并存储纠正信息
4. 当显示AI建议时，应包含推理或分类解释
5. 当AI接口尚未实现时，系统应使用基于规则的分类作为占位符

### 8. 导出功能

**用户故事**：作为用户，我希望将整理好的书签导出为HTML文件，以便能够将新的组织结构导入回浏览器。

**验收标准**：
1. 当用户点击导出时，系统应生成与主流浏览器兼容的HTML文件
2. 当生成导出文件时，应保留用户创建的确切分类结构
3. 当创建导出文件时，系统应将其存储在R2中并提供下载链接
4. 当用户下载文件时，文件应带有时间戳命名以便识别
5. 当导出文件导入到浏览器时，文件夹结构应与整理的分类匹配

### 9. 撤销/重做系统

**用户故事**：作为用户，我希望能够撤销和重做我的整理操作，以便在不担心丢失工作的情况下尝试不同的整理方法。

**验收标准**：
1. 当用户执行任何整理操作时，系统应将其添加到操作历史栈中
2. 当用户点击撤销时，系统应恢复上一个操作并更新显示
3. 当用户点击重做时，系统应重新应用之前撤销的操作
4. 当用户点击重置时，系统应返回到文件上传后的初始状态
5. 当操作历史达到容量时，系统应删除最旧的操作以保持性能

### 10. 性能和可靠性

**用户故事**：作为用户，我希望系统能够高效可靠地处理我的书签，以便在没有性能问题的情况下整理大型书签集合。

**验收标准**：
1. 当处理多达1000个书签时，系统应在1分钟内完成解析和初始分类
2. 当用户执行UI交互时，系统应在200毫秒内响应
3. 当文件上传失败时，系统应提供清晰的错误消息和重试选项
4. 当系统遇到错误时，应适当记录错误并保持用户数据完整性
5. 当用户访问系统时，应在Chrome、Firefox、Edge和Safari浏览器上正常工作

### 11. 数据安全和隐私

**用户故事**：作为用户，我希望我的书签数据是安全和私密的，以便能够信任系统处理我的个人浏览信息。

**验收标准**：
1. 当传输用户数据时，应使用HTTPS加密
2. 当存储书签文件时，应按用户隔离，不允许跨用户访问
3. 当用户删除数据时，系统应删除所有相关文件和数据库记录
4. 当进行身份验证时，系统应使用具有适当过期时间的安全JWT令牌
5. 当用户不活跃时，其会话应自动过期以确保安全
