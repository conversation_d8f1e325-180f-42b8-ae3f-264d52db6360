// Prisma Schema for Bookmark Manager MVP
// 基于 Supabase PostgreSQL 数据库

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户信息表 (扩展 Supabase Auth)
model Profile {
  id           String   @id @db.Uuid
  email        String   @unique
  displayName  String?  @map("display_name")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt    DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关系
  workspace   UserBookmarkWorkspace?
  categories  UserCategory[]

  @@map("profiles")
}

// 用户工作空间表
model UserBookmarkWorkspace {
  id                   String   @id @default(cuid()) @db.Uuid
  userId               String   @unique @map("user_id") @db.Uuid
  status               String   @default("idle") // idle, processing, completed, failed
  originalFileKey      String?  @map("original_file_key")
  resultFileKey        String?  @map("result_file_key")
  originalFilename     String?  @map("original_filename")
  browserType          String?  @map("browser_type")
  totalBookmarks       Int      @default(0) @map("total_bookmarks")
  processedBookmarks   Int      @default(0) @map("processed_bookmarks")
  aiLearningExamples   Json     @default("[]") @map("ai_learning_examples")
  processingMetadata   Json     @default("{}") @map("processing_metadata")
  lastImportAt         DateTime? @map("last_import_at") @db.Timestamptz
  createdAt            DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt            DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关系
  user      Profile @relation(fields: [userId], references: [id], onDelete: Cascade)
  bookmarks BookmarkProcessingCache[]

  @@map("user_bookmark_workspace")
}

// 用户分类表
model UserCategory {
  id          String   @id @default(cuid()) @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  name        String
  type        String   @default("manual") // manual, ai, alphabetical
  color       String?
  description String?
  sortOrder   Int      @default(0) @map("sort_order")
  isDefault   Boolean  @default(false) @map("is_default")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关系
  user Profile @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, name])
  @@map("user_categories")
}

// 书签处理缓存表
model BookmarkProcessingCache {
  id                   String   @id @default(cuid()) @db.Uuid
  workspaceId          String   @map("workspace_id") @db.Uuid
  title                String
  url                  String
  originalPath         String?  @map("original_path")
  status               String   @default("unprocessed") // unprocessed, confirmed, rejected
  aiSuggestion         Json?    @map("ai_suggestion")
  suggestedCategory    String?  @map("suggested_category")
  finalCategory        String?  @map("final_category")
  classificationType   String   @default("manual") @map("classification_type") // manual, ai, alphabetical
  isDuplicate          Boolean  @default(false) @map("is_duplicate")
  duplicateUrl         String?  @map("duplicate_url")
  createdAt            DateTime @default(now()) @map("created_at") @db.Timestamptz

  // 关系
  workspace UserBookmarkWorkspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@map("bookmark_processing_cache")
}
