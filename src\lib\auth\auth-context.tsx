'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthError } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase/client';
import {
  AuthProvider,
  Supabase<PERSON><PERSON><PERSON>rovider,
  AuthUser,
  AuthSession
} from '@/lib/abstractions/auth-provider';

interface AuthContextType {
  user: AuthUser | null;
  session: AuthSession | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signUp: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  signInWithGoogle: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 创建认证提供商实例
const authProvider = new SupabaseAuthProvider(supabase);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 获取初始会话
    const getInitialSession = async () => {
      const { session, error } = await authProvider.getSession();

      if (error) {
        console.error('Error getting session:', error);
      } else {
        setSession(session);
        setUser(session?.user ?? null);
      }

      setLoading(false);
    };

    getInitialSession();

    // 监听认证状态变化
    const unsubscribe = authProvider.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);

        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // 处理特殊事件
        if (event === 'SIGNED_IN') {
          // 用户登录成功
          console.log('User signed in:', session?.user?.email);
        } else if (event === 'SIGNED_OUT') {
          // 用户登出
          console.log('User signed out');
        } else if (event === 'TOKEN_REFRESHED') {
          // Token 刷新
          console.log('Token refreshed');
        }
      }
    );

    return () => {
      unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await authProvider.signIn(email, password);
    return { error };
  };

  const signUp = async (email: string, password: string) => {
    const { error } = await authProvider.signUp(email, password);
    return { error };
  };

  const signOut = async () => {
    const { error } = await authProvider.signOut();
    return { error };
  };

  const signInWithGoogle = async () => {
    const { error } = await authProvider.signInWithGoogle();
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { error } = await authProvider.resetPassword(email);
    return { error };
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
