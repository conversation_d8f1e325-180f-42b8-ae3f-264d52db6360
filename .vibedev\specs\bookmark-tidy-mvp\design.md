# 设计文档：智能书签管家 MVP

## 概述

智能书签管家 MVP 是一个基于 Next.js 15 的全栈 Web 应用，采用现代 Serverless 架构。系统通过 Supabase 提供数据库和认证服务，使用 Cloudflare R2 进行文件存储，并集成 OpenAI API 实现智能分类功能。该设计遵循 7 阶段渐进式开发策略，AI 功能最后实现，前期使用规则引擎作为占位符。

### 核心设计原则
- **简化优先**: 每用户一个工作空间，避免复杂的多会话管理
- **成本优化**: 使用 R2 存储文件，数据库仅存储元数据
- **渐进增强**: AI 功能预留接口，先用规则引擎实现基础功能
- **用户体验**: 前端撤销机制，响应式设计，直观的拖拽操作

## 架构

### 系统架构图

```mermaid
graph TD
    subgraph "客户端层"
        UI[React/Next.js 前端]
        UndoStack[撤销/重做栈]
    end

    subgraph "应用层 (Vercel)"
        API[Next.js API Routes]
        Auth[认证中间件]
        FileProcessor[文件处理器]
        ClassifierInterface[分类器接口]
    end

    subgraph "数据层"
        DB[(Supabase PostgreSQL)]
        R2[(Cloudflare R2)]
    end

    subgraph "外部服务"
        SupabaseAuth[Supabase Auth]
        OpenAI[OpenAI API]
    end

    UI --> API
    UI --> UndoStack
    API --> Auth
    API --> FileProcessor
    API --> ClassifierInterface
    Auth --> SupabaseAuth
    FileProcessor --> R2
    ClassifierInterface --> OpenAI
    API --> DB
```

### 技术栈选择

| 层级 | 技术 | 选择理由 |
|------|------|----------|
| **前端框架** | Next.js 15 (App Router) | 全栈集成，Serverless 原生支持，React 生态丰富 |
| **UI 组件** | Tailwind CSS + Shadcn/ui | 原子化样式，高质量组件，易于定制 |
| **状态管理** | Zustand + TanStack Query | 轻量级状态管理，强大的数据获取和缓存 |
| **数据库** | Supabase PostgreSQL | 开源 BaaS，无厂商锁定，优秀的免费套餐 |
| **文件存储** | Cloudflare R2 | S3 兼容，成本极低，无出站流量费用 |
| **认证** | Supabase Auth | 与数据库集成，支持 OAuth，安全可靠 |
| **AI 服务** | OpenAI API | 强大的理解能力，成熟的 API，丰富的文档 |
| **部署** | Vercel | 与 Next.js 完美集成，自动 CI/CD |

### 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant R as R2存储
    participant D as 数据库
    participant AI as AI服务

    U->>F: 上传书签文件
    F->>A: POST /api/workspace/upload
    A->>R: 存储原始文件
    A->>A: 解析HTML内容
    A->>D: 存储到缓存表
    A->>AI: 请求分类建议
    AI->>A: 返回分类结果
    A->>D: 更新分类建议
    A->>F: 返回处理状态
    F->>U: 显示分类结果
```

## 组件和接口

### 前端组件架构

```mermaid
graph TD
    App[App Layout] --> Auth[认证组件]
    App --> Dashboard[仪表盘]
    
    Dashboard --> Upload[文件上传组件]
    Dashboard --> Workspace[工作空间组件]
    
    Workspace --> BookmarkList[书签列表]
    Workspace --> CategoryTree[分类树]
    Workspace --> ActionBar[操作栏]
    
    BookmarkList --> BookmarkCard[书签卡片]
    CategoryTree --> CategoryNode[分类节点]
    ActionBar --> UndoRedo[撤销/重做]
    ActionBar --> Export[导出按钮]
    
    BookmarkCard --> DragHandle[拖拽手柄]
    CategoryNode --> DropZone[拖放区域]
```

### 核心组件设计

#### 1. 文件上传组件 (FileUpload)
```typescript
interface FileUploadProps {
  onUpload: (file: File) => Promise<void>;
  maxSize: number; // 10MB
  acceptedTypes: string[]; // ['.html']
  isUploading: boolean;
}

// 功能特性
- 拖拽上传支持
- 进度条显示
- 错误处理和重试
- 文件类型验证
```

#### 2. 书签列表组件 (BookmarkList)
```typescript
interface BookmarkListProps {
  bookmarks: Bookmark[];
  onBookmarkUpdate: (id: string, updates: Partial<Bookmark>) => void;
  onBatchUpdate: (ids: string[], categoryName: string) => void;
  searchQuery: string;
  filterCategory?: string;
}

// 功能特性
- 虚拟滚动（大数据量优化）
- 多选支持
- 搜索和筛选
- 拖拽排序
```

#### 3. 分类树组件 (CategoryTree)
```typescript
interface CategoryTreeProps {
  categories: Category[];
  onCategoryCreate: (name: string, color: string) => void;
  onCategoryUpdate: (id: string, updates: Partial<Category>) => void;
  onCategoryDelete: (id: string) => void;
  onDrop: (bookmarkIds: string[], categoryId: string) => void;
}

// 功能特性
- 树形结构展示
- 拖放目标区域
- 内联编辑
- 颜色选择器
```

#### 4. 撤销/重做系统 (UndoRedoManager)
```typescript
interface UndoRedoState {
  history: BookmarkState[];
  currentIndex: number;
  maxHistorySize: number; // 50
  hasUnsavedChanges: boolean; // 新增：标记是否有未保存更改
}

interface BookmarkState {
  bookmarks: Bookmark[];
  categories: Category[];
  timestamp: number;
}

// 功能特性
- 操作历史栈管理（仅前端）
- 状态快照和恢复
- 内存优化（限制历史大小）
- 键盘快捷键支持 (Ctrl+Z, Ctrl+Y)
- 未保存更改提醒
```

#### 5. 保存确认对话框 (SaveConfirmDialog)
```typescript
interface SaveConfirmDialogProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  changesCount: number; // 更改的书签数量
  newCategoriesCount: number; // 新建的分类数量
}

// 功能特性
- 显示更改摘要
- 不可回滚警告
- 数据管理提醒
- 确认/取消操作
```

#### 6. 工作空间状态管理 (WorkspaceManager)
```typescript
interface WorkspaceState {
  // 服务端数据（只读）
  originalBookmarks: BookmarkCache[];
  originalCategories: UserCategory[];

  // 前端工作状态（可编辑）
  workingBookmarks: BookmarkCache[];
  workingCategories: UserCategory[];

  // 状态标记
  hasUnsavedChanges: boolean;
  isSaving: boolean;
  lastSavedAt?: Date;
}

// 功能特性
- 区分原始数据和工作数据
- 变更检测和提醒
- 自动保存提醒（可选）
- 离开页面确认
```

### API 接口设计 - 重新设计版本

基于用户体验优化，采用"前端操作 + 批量保存"的架构模式。

#### 认证相关
```typescript
// GET /api/auth/session - 获取当前用户会话
// POST /api/auth/signout - 用户登出
```

#### 核心工作流程API
```typescript
// 1. 上传书签文件
// POST /api/workspace/upload
interface UploadRequest {
  file: File; // FormData
}
interface UploadResponse {
  workspaceId: string;
  status: 'processing' | 'completed' | 'failed';
  totalBookmarks: number;
  message?: string;
}

// 2. 获取工作空间数据（用于前端初始化）
// GET /api/workspace
interface WorkspaceResponse {
  workspace: UserWorkspace;
  bookmarks: BookmarkCache[];
  categories: UserCategory[];
}

// 3. 保存所有更改（关键API - 批量保存）
// POST /api/workspace/save
interface SaveRequest {
  bookmarks: {
    id: string;
    finalCategory: string;
    status: 'confirmed' | 'rejected';
  }[];
  categories: {
    id?: string; // 新分类没有ID
    name: string;
    type: 'manual' | 'ai' | 'alphabetical';
    color?: string;
    description?: string;
  }[];
  userConfirmed: boolean; // 用户确认不可回滚操作
}
interface SaveResponse {
  success: boolean;
  message: string;
  resultFileUrl?: string; // 生成的结果文件下载链接
}

// 4. 重置工作空间（回到上传后的初始状态）
// POST /api/workspace/reset
interface ResetResponse {
  success: boolean;
  message: string;
}

// 5. 导出整理结果
// GET /api/workspace/export
interface ExportResponse {
  downloadUrl: string;
  filename: string;
  expiresAt: string;
}
```

#### 用户操作流程设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant R as R2存储
    participant D as 数据库

    Note over U,D: 1. 文件上传和初始化
    U->>F: 上传书签文件
    F->>A: POST /api/workspace/upload
    A->>R: 存储原始文件
    A->>A: 解析HTML + AI分类
    A->>D: 存储到缓存表
    A->>F: 返回初始数据
    F->>U: 显示书签列表

    Note over U,D: 2. 前端操作阶段（无API调用）
    U->>F: 拖拽书签、修改分类
    F->>F: 仅更新本地状态
    U->>F: 创建新分类
    F->>F: 仅更新本地状态
    U->>F: 继续整理...
    F->>F: 仅更新本地状态

    Note over U,D: 3. 保存确认阶段
    U->>F: 点击"保存"按钮
    F->>U: 显示确认对话框
    Note over F,U: "保存后无法回滚，确认继续？"
    U->>F: 确认保存
    F->>A: POST /api/workspace/save
    A->>D: 批量更新数据库
    A->>R: 生成result.html
    A->>D: 清空缓存表
    A->>F: 返回成功结果
    F->>U: 显示保存成功 + 下载链接
```

## 数据模型

### 数据库表结构

#### 用户信息表 (profiles)
```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email VARCHAR NOT NULL,
  display_name VARCHAR,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 用户工作空间表 (user_bookmark_workspace)
```sql
CREATE TABLE user_bookmark_workspace (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) UNIQUE,
  status VARCHAR DEFAULT 'idle',
  original_file_key VARCHAR,
  result_file_key VARCHAR,
  original_filename VARCHAR,
  browser_type VARCHAR,
  total_bookmarks INTEGER DEFAULT 0,
  processed_bookmarks INTEGER DEFAULT 0,
  ai_learning_examples JSONB,
  processing_metadata JSONB,
  last_import_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 用户分类表 (user_categories)
```sql
CREATE TABLE user_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id),
  name VARCHAR NOT NULL,
  type VARCHAR DEFAULT 'manual',
  color VARCHAR,
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, name)
);
```

#### 书签处理缓存表 (bookmark_processing_cache)
```sql
CREATE TABLE bookmark_processing_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID NOT NULL REFERENCES user_bookmark_workspace(id),
  title TEXT NOT NULL,
  url TEXT NOT NULL,
  original_path TEXT,
  status VARCHAR DEFAULT 'unprocessed',
  ai_suggestion JSONB,
  suggested_category VARCHAR,
  final_category VARCHAR,
  classification_type VARCHAR DEFAULT 'manual',
  is_duplicate BOOLEAN DEFAULT FALSE,
  duplicate_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### TypeScript 类型定义

```typescript
// 核心数据类型
interface UserWorkspace {
  id: string;
  userId: string;
  status: 'idle' | 'processing' | 'completed' | 'failed';
  originalFileKey?: string;
  resultFileKey?: string;
  originalFilename?: string;
  browserType?: string;
  totalBookmarks: number;
  processedBookmarks: number;
  aiLearningExamples?: AILearningExample[];
  processingMetadata?: Record<string, any>;
  lastImportAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserCategory {
  id: string;
  userId: string;
  name: string;
  type: 'manual' | 'ai' | 'alphabetical';
  color?: string;
  description?: string;
  sortOrder: number;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface BookmarkCache {
  id: string;
  workspaceId: string;
  title: string;
  url: string;
  originalPath?: string;
  status: 'unprocessed' | 'confirmed' | 'rejected';
  aiSuggestion?: {
    category: string;
    confidence: number;
    reasons?: string[];
  };
  suggestedCategory?: string;
  finalCategory?: string;
  classificationType: 'manual' | 'ai' | 'alphabetical';
  isDuplicate: boolean;
  duplicateUrl?: string;
  createdAt: string;
}

interface AILearningExample {
  title: string;
  url: string;
  category: string;
  timestamp: string;
}
```

## 错误处理

### 错误分类和处理策略

#### 1. 客户端错误处理
```typescript
// 全局错误边界
class GlobalErrorBoundary extends React.Component {
  // 捕获 React 组件错误
  // 显示友好的错误页面
  // 记录错误日志
}

// API 错误处理
interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// 错误类型定义
enum ErrorCodes {
  // 认证错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',

  // 文件处理错误
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  PARSE_ERROR = 'PARSE_ERROR',

  // 业务逻辑错误
  WORKSPACE_NOT_FOUND = 'WORKSPACE_NOT_FOUND',
  CATEGORY_EXISTS = 'CATEGORY_EXISTS',

  // 系统错误
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}
```

#### 2. 服务端错误处理
```typescript
// API 路由错误处理中间件
export function withErrorHandler(handler: NextApiHandler) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      return await handler(req, res);
    } catch (error) {
      console.error('API Error:', error);

      if (error instanceof ValidationError) {
        return res.status(400).json({
          code: 'VALIDATION_ERROR',
          message: error.message,
          details: error.details
        });
      }

      if (error instanceof AuthError) {
        return res.status(401).json({
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        });
      }

      // 默认内部服务器错误
      return res.status(500).json({
        code: 'INTERNAL_ERROR',
        message: 'Internal server error'
      });
    }
  };
}
```

#### 3. 文件处理错误
```typescript
// 文件上传和解析错误处理
class FileProcessingError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: Record<string, any>
  ) {
    super(message);
  }
}

// 错误恢复策略
const errorRecoveryStrategies = {
  PARSE_ERROR: '请检查文件格式是否正确，支持 Chrome/Firefox/Edge 导出的书签文件',
  FILE_TOO_LARGE: '文件大小超过 10MB 限制，请尝试导出较少的书签',
  NETWORK_ERROR: '网络连接异常，请检查网络后重试',
  AI_SERVICE_ERROR: '智能分类服务暂时不可用，将使用基础分类功能'
};
```

### 用户体验优化

#### 1. 加载状态管理
```typescript
interface LoadingState {
  isUploading: boolean;
  isParsing: boolean;
  isClassifying: boolean;
  isSaving: boolean; // 新增：保存状态
  isExporting: boolean;
  progress?: number;
  message?: string;
}

// 全局加载状态管理
const useLoadingState = () => {
  const [loading, setLoading] = useState<LoadingState>({
    isUploading: false,
    isParsing: false,
    isClassifying: false,
    isSaving: false,
    isExporting: false
  });

  // 提供统一的加载状态更新方法
  // 自动处理加载超时
  // 错误状态自动清除
};
```

#### 2. 保存操作特殊处理
```typescript
// 保存确认对话框内容
const SaveConfirmDialog = ({ isOpen, onConfirm, onCancel, changesCount, newCategoriesCount }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>⚠️ 确认保存更改</DialogTitle>
          <DialogDescription>
            您即将保存 {changesCount} 个书签的更改和 {newCategoriesCount} 个新分类。
          </DialogDescription>
        </DialogHeader>

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                重要提醒
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>保存后将无法回滚到之前的状态</li>
                  <li>原始书签文件将被新的整理结果覆盖</li>
                  <li>建议您先下载当前结果作为备份</li>
                  <li>如需重新整理，请重新上传原始文件</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button onClick={onConfirm} className="bg-red-600 hover:bg-red-700">
            确认保存（不可回滚）
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// 页面离开确认
const useBeforeUnload = (hasUnsavedChanges: boolean) => {
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);
};
```

#### 2. 离线支持和数据同步
```typescript
// 离线状态检测
const useOnlineStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
};

// 本地数据缓存和同步
const useOfflineSync = () => {
  // 使用 IndexedDB 缓存用户数据
  // 网络恢复时自动同步
  // 冲突解决策略
};
```

## 测试策略

### 测试金字塔

```mermaid
graph TD
    E2E[端到端测试<br/>Playwright/Cypress<br/>关键用户流程]
    Integration[集成测试<br/>API 路由测试<br/>数据库集成]
    Unit[单元测试<br/>Jest + Testing Library<br/>组件和工具函数]

    E2E --> Integration
    Integration --> Unit
```

### 1. 单元测试 (Jest + React Testing Library)
```typescript
// 组件测试示例
describe('BookmarkCard', () => {
  it('should display bookmark information correctly', () => {
    const bookmark = {
      id: '1',
      title: 'Test Bookmark',
      url: 'https://example.com',
      suggestedCategory: 'Technology'
    };

    render(<BookmarkCard bookmark={bookmark} />);

    expect(screen.getByText('Test Bookmark')).toBeInTheDocument();
    expect(screen.getByText('https://example.com')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
  });

  it('should handle drag and drop correctly', () => {
    // 测试拖拽功能
  });
});

// 工具函数测试
describe('parseBookmarkHTML', () => {
  it('should parse Chrome bookmark format correctly', () => {
    const html = `<DL><DT><A HREF="https://example.com">Example</A></DT></DL>`;
    const result = parseBookmarkHTML(html);

    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      title: 'Example',
      url: 'https://example.com',
      originalPath: ''
    });
  });
});
```

### 2. 集成测试 (API 路由)
```typescript
// API 路由测试
describe('/api/workspace/upload', () => {
  it('should upload and process bookmark file', async () => {
    const formData = new FormData();
    formData.append('file', new File(['<html>...</html>'], 'bookmarks.html'));

    const response = await fetch('/api/workspace/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${testToken}`
      }
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.workspaceId).toBeDefined();
    expect(data.status).toBe('processing');
  });
});

// 数据库集成测试
describe('Database Operations', () => {
  beforeEach(async () => {
    // 清理测试数据
    await cleanupTestData();
  });

  it('should create workspace for new user', async () => {
    const userId = 'test-user-id';
    const workspace = await createUserWorkspace(userId);

    expect(workspace.userId).toBe(userId);
    expect(workspace.status).toBe('idle');
  });
});
```

### 3. 端到端测试 (Playwright)
```typescript
// E2E 测试关键用户流程
test('complete bookmark organization flow', async ({ page }) => {
  // 1. 用户登录
  await page.goto('/login');
  await page.fill('[data-testid=email]', '<EMAIL>');
  await page.fill('[data-testid=password]', 'password');
  await page.click('[data-testid=login-button]');

  // 2. 上传书签文件
  await page.goto('/dashboard');
  const fileInput = page.locator('[data-testid=file-upload]');
  await fileInput.setInputFiles('test-bookmarks.html');

  // 3. 等待处理完成
  await page.waitForSelector('[data-testid=bookmark-list]');

  // 4. 拖拽书签到分类
  const bookmark = page.locator('[data-testid=bookmark-card]').first();
  const category = page.locator('[data-testid=category-node]').first();
  await bookmark.dragTo(category);

  // 5. 导出结果
  await page.click('[data-testid=export-button]');
  const download = await page.waitForEvent('download');
  expect(download.suggestedFilename()).toContain('bookmarks');
});
```

### 4. 性能测试
```typescript
// 性能基准测试
describe('Performance Tests', () => {
  it('should parse 1000 bookmarks within 1 minute', async () => {
    const startTime = Date.now();
    const largeBookmarkFile = generateLargeBookmarkFile(1000);

    const result = await parseBookmarkHTML(largeBookmarkFile);
    const endTime = Date.now();

    expect(result).toHaveLength(1000);
    expect(endTime - startTime).toBeLessThan(60000); // 1 minute
  });

  it('should handle UI interactions within 200ms', async () => {
    // 测试 UI 响应时间
  });
});
```

### 测试数据管理
```typescript
// 测试数据工厂
class TestDataFactory {
  static createBookmark(overrides?: Partial<BookmarkCache>): BookmarkCache {
    return {
      id: generateId(),
      workspaceId: 'test-workspace',
      title: 'Test Bookmark',
      url: 'https://example.com',
      status: 'unprocessed',
      classificationType: 'manual',
      isDuplicate: false,
      createdAt: new Date().toISOString(),
      ...overrides
    };
  }

  static createWorkspace(overrides?: Partial<UserWorkspace>): UserWorkspace {
    return {
      id: generateId(),
      userId: 'test-user',
      status: 'idle',
      totalBookmarks: 0,
      processedBookmarks: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    };
  }
}
```

## 设计决策和理由

### 1. 架构决策

**决策**: 采用 Serverless 架构而非传统服务器架构
**理由**:
- 降低运维成本和复杂度
- 自动扩缩容，适应不同负载
- 与 Vercel 部署平台完美集成
- 适合 MVP 快速迭代

**决策**: 使用 Cloudflare R2 而非 Supabase Storage
**理由**:
- 成本更低（$0.015/GB/月 vs Supabase 的更高费用）
- 无出站流量费用
- S3 兼容 API，技术成熟
- 全球 CDN 加速

### 2. 数据库设计决策

**决策**: 每用户一个工作空间而非多会话支持
**理由**:
- 简化数据模型和业务逻辑
- 降低存储成本和查询复杂度
- 符合大多数用户的实际使用场景
- 便于实现和维护

**决策**: 使用临时缓存表而非永久存储书签数据
**理由**:
- 大幅降低数据库存储成本
- 避免数据膨胀和性能问题
- 符合"整理工具"而非"存储工具"的定位
- 简化数据生命周期管理

### 3. 前端架构决策

**决策**: 采用"前端操作 + 批量保存"模式而非实时同步
**理由**:
- **性能优化**: 减少频繁的网络请求和数据库写入
- **用户体验**: 操作即时响应，类似文档编辑器体验
- **成本控制**: 大幅减少API调用次数和服务器负载
- **灵活性**: 用户可以反复试验不同的分类方案
- **符合工具定位**: 整理工具应该支持"试验-确认"的工作流

**决策**: 前端实现撤销/重做而非服务端备份
**理由**:
- 响应速度更快，用户体验更好
- 减少服务端存储和计算压力
- 类似文档编辑器的熟悉交互模式
- 降低系统复杂度

**决策**: 使用 Zustand + TanStack Query 而非 Redux
**理由**:
- Zustand 更轻量，学习成本低
- TanStack Query 提供强大的数据获取和缓存能力
- 减少样板代码，提高开发效率
- 更好的 TypeScript 支持

**决策**: 强制用户确认保存操作
**理由**:
- **数据安全**: 避免意外覆盖重要数据
- **用户教育**: 让用户理解数据管理责任
- **成本控制**: R2存储策略需要用户主动管理
- **透明度**: 明确告知操作的不可逆性

## 未来版本规划

### V1.1 功能增强 (MVP 后的第一次迭代)

#### 1. 国际化和多语言支持
```typescript
// 多语言架构设计
interface I18nConfig {
  defaultLocale: 'zh-CN' | 'en-US' | 'ja-JP';
  supportedLocales: string[];
  fallbackLocale: string;
}

// 语言资源结构
interface LanguageResources {
  common: {
    save: string;
    cancel: string;
    confirm: string;
    // ...
  };
  auth: {
    login: string;
    register: string;
    // ...
  };
  workspace: {
    upload: string;
    categories: string;
    // ...
  };
}
```

**实现要点**:
- 使用 `next-i18next` 或 `react-i18next` 实现国际化
- 支持中文、英文、日文三种语言
- 动态语言切换，用户偏好持久化
- RTL 语言支持预留接口

#### 2. 中间件系统架构
```typescript
// 中间件接口设计
interface Middleware {
  name: string;
  priority: number;
  execute: (context: MiddlewareContext) => Promise<MiddlewareResult>;
}

interface MiddlewareContext {
  request: NextApiRequest;
  response: NextApiResponse;
  user?: User;
  workspace?: UserWorkspace;
}

// 中间件类型
enum MiddlewareType {
  AUTH = 'auth',           // 认证中间件
  RATE_LIMIT = 'rateLimit', // 限流中间件
  LOGGING = 'logging',      // 日志中间件
  ANALYTICS = 'analytics',  // 分析中间件
  CACHE = 'cache'          // 缓存中间件
}
```

**实现要点**:
- 可插拔的中间件架构
- 支持认证、限流、日志、分析等功能
- 中间件优先级和依赖管理
- 开发者友好的中间件 API

#### 3. 广告系统集成
```typescript
// 广告配置接口
interface AdConfig {
  provider: 'google-adsense' | 'custom';
  placements: AdPlacement[];
  targeting: AdTargeting;
  frequency: AdFrequency;
}

interface AdPlacement {
  id: string;
  type: 'banner' | 'native' | 'interstitial';
  position: 'header' | 'sidebar' | 'footer' | 'inline';
  size: AdSize;
  enabled: boolean;
}

// 用户体验优化
interface AdUserExperience {
  maxAdsPerPage: number;
  minContentRatio: number; // 内容与广告的最小比例
  respectUserPreferences: boolean;
  adBlockerDetection: boolean;
}
```

**实现要点**:
- 非侵入式广告展示
- 用户体验优先，广告不影响核心功能
- 支持 Google AdSense 和自定义广告
- 广告屏蔽检测和友好提示
- 付费用户无广告选项

### V1.2 高级功能

#### 4. 高级分析和统计
- 书签使用频率分析
- 分类效果统计
- 用户行为分析
- 导出使用报告

#### 5. 协作和分享功能
- 书签分类模板分享
- 团队协作整理
- 公开分类库
- 社区推荐分类

#### 6. 高级 AI 功能
- 智能标签生成
- 重复书签智能合并
- 个性化推荐分类
- 自然语言分类指令

#### 7. 企业级功能
- 单点登录 (SSO) 支持
- 企业级权限管理
- 批量用户管理
- 审计日志

### V2.0 平台扩展

#### 8. 移动端应用
- React Native 移动应用
- 离线同步功能
- 移动端优化的 UI
- 推送通知

#### 9. 浏览器扩展
- Chrome/Firefox 扩展
- 实时书签同步
- 网页快速分类
- 一键导入功能

#### 10. API 开放平台
- RESTful API 开放
- 第三方集成支持
- 开发者文档和 SDK
- API 使用统计和限流

### 技术债务和优化

#### 性能优化
- 服务端渲染 (SSR) 优化
- 图片和资源懒加载
- CDN 加速
- 数据库查询优化

#### 安全增强
- 双因素认证 (2FA)
- 数据加密增强
- 安全审计
- 漏洞扫描

#### 可观测性
- 应用性能监控 (APM)
- 错误追踪和报警
- 用户行为分析
- 业务指标监控

### 4. AI 集成决策

**决策**: AI 功能最后实现，前期使用规则引擎占位
**理由**:
- 降低开发风险，确保核心功能先完成
- 规则引擎可以提供基础分类能力
- 为 AI 集成预留清晰的接口
- 符合渐进式开发策略

**决策**: 使用 OpenAI API 而非自建模型
**理由**:
- 开发成本低，无需机器学习专业知识
- API 稳定可靠，文档完善
- 支持 Few-shot Learning，适合个性化学习
- 可以根据需要选择不同的模型
