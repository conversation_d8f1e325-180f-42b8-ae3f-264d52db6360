// 应用常量定义

export const APP_CONFIG = {
  name: 'Bookmark Manager',
  description: '智能书签管家 - 让书签整理变得简单',
  version: '0.1.0',
} as const;

export const FILE_UPLOAD = {
  maxSize: 10 * 1024 * 1024, // 10MB
  acceptedTypes: ['.html'],
  acceptedMimeTypes: ['text/html'],
} as const;

export const BOOKMARK_LIMITS = {
  maxBookmarks: 10000,
  maxCategories: 100,
  maxHistorySize: 50,
} as const;

export const UI_CONFIG = {
  debounceDelay: 300,
  animationDuration: 200,
  toastDuration: 5000,
} as const;

export const API_ENDPOINTS = {
  auth: {
    session: '/api/auth/session',
    signout: '/api/auth/signout',
  },
  workspace: {
    base: '/api/workspace',
    upload: '/api/workspace/upload',
    save: '/api/workspace/save',
    reset: '/api/workspace/reset',
    export: '/api/workspace/export',
  },
} as const;

export const ERROR_CODES = {
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  
  // 文件处理错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  PARSE_ERROR: 'PARSE_ERROR',
  
  // 业务逻辑错误
  WORKSPACE_NOT_FOUND: 'WORKSPACE_NOT_FOUND',
  CATEGORY_EXISTS: 'CATEGORY_EXISTS',
  
  // 系统错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const;

export const DEFAULT_CATEGORIES = [
  { name: '未分类', type: 'manual', color: '#6b7280', isDefault: true },
  { name: '收件箱', type: 'manual', color: '#3b82f6', isDefault: true },
] as const;
