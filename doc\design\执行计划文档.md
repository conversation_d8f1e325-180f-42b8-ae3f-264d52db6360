# 智能书签管家 (Bookmark Tidy) - 项目执行计划 (重新规划版本)
**文档版本: 2.0**
**日期: 2025-07-31**

## 1. 文档概述

本执行计划基于用户反馈重新规划，采用7个Phase渐进式开发策略，AI功能预留接口最后实现。确保每个模块独立可测试，项目核心流程优先跑通。

**核心目标 (MVP):** 开发一个功能性的Web应用，用户可以上传浏览器书签，通过手动和AI辅助的方式进行整理，并最终导出一个全新的、干净的HTML书签文件。

## 2. 项目资源与团队

*   **团队构成**: 独立开发（1人角色，负责全栈开发、产品与运维）。
*   **核心技术栈** (优化版本):
    *   **全栈框架**: Next.js 15
    *   **后端即服务**: Supabase (Database, Auth)
    *   **对象存储**: Cloudflare R2 (书签文件存储)
    *   **部署平台**: Vercel
    *   **AI 服务**: OpenAI API
*   **开发工具**:
    *   **代码编辑器**: VS Code
    *   **版本控制**: Git / GitHub
    *   **API 测试**: Postman / Bruno
    *   **设计/线框图**: Figma / Excalidraw (可选)

## 3. 总体开发时间线 (Gantt Chart) - 重新规划版本

这是一个重新评估的时间线，采用7个Phase渐进式开发策略。总计 **约6-8周** 完成MVP。

```mermaid
gantt
    title Bookmark Tidy - MVP Development Timeline (7 Phases)
    dateFormat  YYYY-MM-DD
    section Phase 1: Foundation
    Project Setup        :done, 2025-07-31, 2d
    Database & Config    :2025-08-02, 3d
    section Phase 2: Authentication
    Auth System          :2025-08-05, 4d
    User Management      :2025-08-09, 2d
    section Phase 3: File Processing
    File Upload & R2     :2025-08-11, 3d
    HTML Parsing         :2025-08-14, 3d
    section Phase 4: UI & Manual Features
    Bookmark Interface   :2025-08-17, 4d
    Manual Organization  :2025-08-21, 4d
    section Phase 5: Classification
    Alphabetical Sort    :2025-08-25, 2d
    AI Interface (Mock)  :2025-08-27, 2d
    section Phase 6: Export & Polish
    Export Functionality :2025-08-29, 3d
    Testing & Bug Fix    :2025-09-01, 4d
    section Phase 7: AI Integration
    OpenAI Integration   :2025-09-05, 4d
    AI Learning System   :2025-09-09, 3d
```

## 4. 分阶段执行计划 (Task Breakdown) - 7 Phase 重新规划

### **Phase 1: 项目基础设施 (预计: 5天)**
**目标**: 建立完整的开发基础设施，包括数据库、存储、部署等核心服务。

**任务清单**:
- [x] **T1.1**: 创建 GitHub 仓库并初始化 Next.js 15 项目 (已完成)
- [ ] **T1.2**: 配置开发环境
  - 集成 Tailwind CSS + Shadcn/ui
  - 配置 TypeScript 严格模式
  - 设置 ESLint + Prettier
- [ ] **T1.3**: 创建 Supabase 项目
  - 获取 API Keys 和数据库 URL
  - 配置 Row Level Security (RLS)
- [ ] **T1.4**: 创建 Cloudflare R2 存储桶
  - 获取访问密钥
  - 配置 CORS 策略
- [ ] **T1.5**: 数据库结构创建
  - `profiles` 表 (用户信息)
  - `user_bookmark_workspace` 表 (工作空间)
  - `user_categories` 表 (分类模板)
  - `bookmark_processing_cache` 表 (处理缓存)
- [ ] **T1.6**: 环境变量配置
  - 本地 `.env.local` 文件
  - Vercel 生产环境变量
- [ ] **T1.7**: 部署管道设置
  - 连接 Vercel 自动部署
  - 测试部署流程

**交付成果**: 完整的开发基础设施，数据库结构就绪，可部署的空白项目。

### **Phase 2: 用户认证模块 (预计: 6天)**
**目标**: 实现完整的用户认证系统，包括注册、登录、状态管理。

**任务清单**:
- [ ] **T2.1**: Supabase Auth 集成
  - 安装 `@supabase/auth-helpers-nextjs`
  - 配置 Auth 中间件
  - 设置 Auth 回调路由
- [ ] **T2.2**: 认证页面开发
  - 登录页面 UI (`/login`)
  - 注册页面 UI (`/register`)
  - 密码重置页面 (`/reset-password`)
- [ ] **T2.3**: Google OAuth 集成
  - 配置 Google OAuth 应用
  - 实现一键登录功能
  - 处理 OAuth 回调
- [ ] **T2.4**: 用户状态管理
  - 创建 Auth Context
  - 实现登录状态持久化
  - 路由保护中间件
- [ ] **T2.5**: 用户资料管理
  - 用户资料页面
  - 资料编辑功能
  - 头像上传 (可选)

**交付成果**: 完整的用户认证系统，用户可以注册、登录、管理资料。

### **Phase 3: 文件处理模块 (预计: 6天)**
**目标**: 实现书签文件的上传、解析、存储功能。

**任务清单**:
- [ ] **T3.1**: Cloudflare R2 集成
  - 安装 `@aws-sdk/client-s3` (R2兼容)
  - 配置 R2 客户端
  - 实现文件上传/下载/删除功能
- [ ] **T3.2**: 文件上传 API
  - `/api/workspace/upload` 端点
  - 文件类型验证 (HTML)
  - 文件大小限制 (10MB)
  - 上传进度反馈
- [ ] **T3.3**: HTML 解析服务
  - 集成 `cheerio.js`
  - 解析 Netscape 书签格式
  - 提取书签数据 (标题、URL、路径)
  - 错误处理和验证
- [ ] **T3.4**: 数据存储逻辑
  - 工作空间管理
  - 书签缓存表操作
  - 数据清理和重置功能
- [ ] **T3.5**: 文件上传界面
  - 拖拽上传组件
  - 上传进度显示
  - 错误提示和重试

**交付成果**: 用户可以上传书签文件，系统能解析并存储书签数据。

### **Phase 4: 整理界面模块 (预计: 8天)**
**目标**: 构建书签整理的核心交互界面。

**任务清单**:
- [ ] **T4.1**: 基础布局开发
  - 主应用布局 (导航栏、侧边栏、主内容)
  - 响应式设计适配
  - 主题切换功能 (可选)
- [ ] **T4.2**: 书签展示组件
  - `BookmarkCard` 组件 (显示书签信息)
  - `BookmarkList` 组件 (书签列表)
  - 书签搜索和筛选功能
- [ ] **T4.3**: 分类管理界面
  - `CategoryTree` 组件 (分类树)
  - 分类创建/编辑/删除功能
  - 分类颜色和图标设置
- [ ] **T4.4**: 拖拽整理功能
  - 集成 `@dnd-kit/core` 拖拽库
  - 书签拖拽到分类功能
  - 批量选择和操作
- [ ] **T4.5**: 状态管理
  - 使用 `zustand` 或 `TanStack Query`
  - 书签数据缓存和同步
  - 撤销/重做功能实现

**交付成果**: 完整的书签整理界面，用户可以手动整理书签。

### **Phase 5: 分类功能模块 (预计: 4天)**
**目标**: 实现多种分类方式，AI功能预留接口。

**任务清单**:
- [ ] **T5.1**: 字母分类功能
  - 按首字母自动分类 (A-Z)
  - 中文拼音首字母支持
  - 分类切换界面
- [ ] **T5.2**: 收件箱/待办功能
  - 未分类书签管理
  - 待处理计数器
  - 批量处理功能
- [ ] **T5.3**: AI分类接口预留
  - 定义 `BookmarkClassifier` 接口
  - 实现 `RuleBasedClassifier` (基于域名规则)
  - 预留 `AIClassifier` 接口
- [ ] **T5.4**: 分类建议系统
  - 显示分类建议和置信度
  - 接受/拒绝建议功能
  - 学习示例存储 (为AI准备)

**交付成果**: 多种分类方式可用，AI接口已预留。

### **Phase 6: 导出模块 (预计: 5天)**
**目标**: 实现书签导出和前端撤销机制。

**任务清单**:
- [ ] **T6.1**: HTML生成服务
  - 根据分类结构生成HTML
  - 兼容浏览器导入格式
  - 文件下载功能
- [ ] **T6.2**: 前端撤销系统
  - 操作历史栈实现
  - 撤销/重做按钮
  - 重置到初始状态
- [ ] **T6.3**: 导出界面
  - 导出预览功能
  - 导出选项设置
  - 下载进度显示
- [ ] **T6.4**: 测试和优化
  - 端到端测试
  - 性能优化
  - 错误处理完善

**交付成果**: 完整的导出功能，前端撤销机制完善。

### **Phase 7: AI智能分类模块 (预计: 7天)**
**目标**: 实现AI智能分类，替换mock实现。

**任务清单**:
- [ ] **T7.1**: OpenAI API集成
  - 安装和配置OpenAI SDK
  - API密钥管理
  - 错误处理和重试机制
- [ ] **T7.2**: Prompt设计和测试
  - 在OpenAI Playground中设计Prompt
  - Few-shot学习模板
  - JSON格式输出优化
- [ ] **T7.3**: AI分类服务
  - 实现 `AIClassifier` 类
  - 替换 `RuleBasedClassifier`
  - 批量分类优化
- [ ] **T7.4**: 会话级学习
  - 用户修正记录
  - 动态Prompt构建
  - 学习效果验证
- [ ] **T7.5**: 最终测试和部署
  - AI功能完整测试
  - 性能和成本优化
  - 生产环境部署

**交付成果**: 完整的AI智能分类功能，MVP全部完成。

## 5. 风险管理

| 风险类别 | 风险描述 | 可能性 | 影响程度 | 缓解措施 |
| :--- | :--- | :--- | :--- | :--- |
| **技术风险** | HTML文件格式多样性远超预期，解析逻辑复杂。 | 中 | 高 | 设定明确的MVP支持格式(Chrome/Edge)；预留额外时间进行兼容性调试；在产品中注明当前支持的浏览器类型。 |
| **成本风险** | OpenAI API调用费用超出预期。 | 中 | 中 | 初期要求用户填写自己的API Key；优化Prompt以减少Token消耗；为高频用户提供缓存机制；选择更便宜的模型（如GPT-3.5-Turbo）。|
| **项目管理** | 范围蔓延，在MVP阶段试图加入过多功能。 | 高 | 高 | 严格遵守本执行计划的MVP功能列表；将所有新想法记录在"未来版本"的待办事项中，而非当前开发周期。 |
| **个人风险** | 作为独立开发者，可能因其他事务中断或产生倦怠。 | 高 | 高 | 制定实际的每日/每周工作计划并遵守；保持工作与生活的平衡；为每个阶段设定小目标并庆祝完成，获得持续的正反馈。 |
| **AI依赖风险** | AI功能开发困难，影响整体进度。 | 中 | 中 | **新增缓解措施**: AI功能放在最后Phase，前期用规则引擎替代；预留充足的AI开发时间；准备降级方案(纯手动分类)。 |

## 6. 预算估算 (优化版本)
- **托管费用 (Vercel)**: $0 (Hobby Plan)
- **数据库费用 (Supabase)**: $0 (Free Plan)
- **对象存储费用 (Cloudflare R2)**: ~$0-2/月 (前10GB免费，$0.015/GB/月)
- **AI调用费用 (OpenAI)**: ~$0-5/月 (可依赖免费额度或要求用户提供Key)
- **域名费用**: ~$15 / 年
- **总计MVP开发和运营成本**: **约 $15-50/年**

**成本优化效果**:
- 通过R2存储替代数据库存储，大幅降低存储成本
- 每用户限制一个工作空间，有效控制存储增长
- R2无出站流量费用，降低数据传输成本

## 7. 成功指标 (KPIs)

### **开发阶段指标**
- **Phase完成率**: 每个Phase按时完成 (目标: 100%)
- **代码质量**: TypeScript覆盖率 > 90%，ESLint零警告
- **测试覆盖**: 核心功能单元测试覆盖率 > 80%

### **产品指标 (MVP上线后)**
- **用户注册**: 首月注册用户 > 50人
- **功能使用**: 书签上传成功率 > 95%
- **用户留存**: 7天留存率 > 30%
- **AI效果**: AI分类准确率 > 70% (用户接受率)

## 8. 下一步行动

### **立即执行 (Phase 1)**
1. **完成开发环境配置** - Tailwind CSS + Shadcn/ui集成
2. **创建Supabase项目** - 获取API密钥，配置RLS
3. **创建Cloudflare R2存储桶** - 配置访问权限
4. **数据库结构创建** - 执行SQL脚本创建4个核心表

### **本周目标**
- 完成Phase 1所有任务
- 部署基础项目到Vercel
- 准备进入Phase 2用户认证开发

---

**文档状态**: ✅ 已更新完成
**下次更新**: 根据开发进度和用户反馈调整
