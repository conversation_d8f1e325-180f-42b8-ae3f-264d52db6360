-- 辅助函数和视图
-- 简化常见的数据库操作

-- 获取用户工作空间的完整信息
CREATE OR REPLACE FUNCTION get_user_workspace_info(user_uuid UUID)
RETURNS TABLE (
    workspace_id UUID,
    status VARCHAR,
    total_bookmarks INTEGER,
    processed_bookmarks INTEGER,
    categories_count BIGINT,
    last_import_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        w.id as workspace_id,
        w.status,
        w.total_bookmarks,
        w.processed_bookmarks,
        COALESCE(cat_count.count, 0) as categories_count,
        w.last_import_at
    FROM user_bookmark_workspace w
    LEFT JOIN (
        SELECT user_id, COUNT(*) as count
        FROM user_categories
        GROUP BY user_id
    ) cat_count ON w.user_id = cat_count.user_id
    WHERE w.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 清空用户的书签缓存（重置工作空间时使用）
CREATE OR REPLACE FUNCTION clear_user_bookmark_cache(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    workspace_uuid UUID;
BEGIN
    -- 获取用户的工作空间ID
    SELECT id INTO workspace_uuid
    FROM user_bookmark_workspace
    WHERE user_id = user_uuid;
    
    IF workspace_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- 删除所有书签缓存
    DELETE FROM bookmark_processing_cache
    WHERE workspace_id = workspace_uuid;
    
    -- 重置工作空间状态
    UPDATE user_bookmark_workspace
    SET 
        status = 'idle',
        total_bookmarks = 0,
        processed_bookmarks = 0,
        processing_metadata = '{}'::jsonb
    WHERE id = workspace_uuid;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 批量插入书签缓存
CREATE OR REPLACE FUNCTION batch_insert_bookmarks(
    workspace_uuid UUID,
    bookmarks_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
    bookmark JSONB;
    inserted_count INTEGER := 0;
BEGIN
    -- 遍历书签数据并插入
    FOR bookmark IN SELECT * FROM jsonb_array_elements(bookmarks_data)
    LOOP
        INSERT INTO bookmark_processing_cache (
            workspace_id,
            title,
            url,
            original_path,
            status
        ) VALUES (
            workspace_uuid,
            bookmark->>'title',
            bookmark->>'url',
            bookmark->>'original_path',
            'unprocessed'
        );
        
        inserted_count := inserted_count + 1;
    END LOOP;
    
    -- 更新工作空间的书签总数
    UPDATE user_bookmark_workspace
    SET total_bookmarks = inserted_count
    WHERE id = workspace_uuid;
    
    RETURN inserted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取书签统计信息
CREATE OR REPLACE FUNCTION get_bookmark_stats(workspace_uuid UUID)
RETURNS TABLE (
    total_count BIGINT,
    unprocessed_count BIGINT,
    confirmed_count BIGINT,
    rejected_count BIGINT,
    duplicate_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_count,
        COUNT(*) FILTER (WHERE status = 'unprocessed') as unprocessed_count,
        COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_count,
        COUNT(*) FILTER (WHERE status = 'rejected') as rejected_count,
        COUNT(*) FILTER (WHERE is_duplicate = true) as duplicate_count
    FROM bookmark_processing_cache
    WHERE workspace_id = workspace_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检测重复书签
CREATE OR REPLACE FUNCTION detect_duplicate_bookmarks(workspace_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    duplicate_count INTEGER := 0;
BEGIN
    -- 标记重复的书签
    WITH duplicates AS (
        SELECT url, MIN(created_at) as first_created
        FROM bookmark_processing_cache
        WHERE workspace_id = workspace_uuid
        GROUP BY url
        HAVING COUNT(*) > 1
    )
    UPDATE bookmark_processing_cache
    SET 
        is_duplicate = true,
        duplicate_url = url
    WHERE workspace_id = workspace_uuid
    AND url IN (SELECT url FROM duplicates)
    AND created_at > (
        SELECT first_created 
        FROM duplicates 
        WHERE duplicates.url = bookmark_processing_cache.url
    );
    
    GET DIAGNOSTICS duplicate_count = ROW_COUNT;
    RETURN duplicate_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建视图：用户工作空间概览
CREATE OR REPLACE VIEW user_workspace_overview AS
SELECT 
    p.id as user_id,
    p.email,
    p.display_name,
    w.id as workspace_id,
    w.status,
    w.total_bookmarks,
    w.processed_bookmarks,
    w.last_import_at,
    (
        SELECT COUNT(*) 
        FROM user_categories 
        WHERE user_id = p.id AND is_default = false
    ) as custom_categories_count,
    (
        SELECT COUNT(*) 
        FROM bookmark_processing_cache 
        WHERE workspace_id = w.id AND status = 'unprocessed'
    ) as pending_bookmarks_count
FROM profiles p
LEFT JOIN user_bookmark_workspace w ON p.id = w.user_id;
