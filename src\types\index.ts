// 核心数据类型定义

export interface UserWorkspace {
  id: string;
  userId: string;
  status: 'idle' | 'processing' | 'completed' | 'failed';
  originalFileKey?: string;
  resultFileKey?: string;
  originalFilename?: string;
  browserType?: string;
  totalBookmarks: number;
  processedBookmarks: number;
  aiLearningExamples?: AILearningExample[];
  processingMetadata?: Record<string, any>;
  lastImportAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserCategory {
  id: string;
  userId: string;
  name: string;
  type: 'manual' | 'ai' | 'alphabetical';
  color?: string;
  description?: string;
  sortOrder: number;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BookmarkCache {
  id: string;
  workspaceId: string;
  title: string;
  url: string;
  originalPath?: string;
  status: 'unprocessed' | 'confirmed' | 'rejected';
  aiSuggestion?: {
    category: string;
    confidence: number;
    reasons?: string[];
  };
  suggestedCategory?: string;
  finalCategory?: string;
  classificationType: 'manual' | 'ai' | 'alphabetical';
  isDuplicate: boolean;
  duplicateUrl?: string;
  createdAt: string;
}

export interface AILearningExample {
  title: string;
  url: string;
  category: string;
  timestamp: string;
}

// API 响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
}

// 文件上传相关
export interface UploadResponse {
  workspaceId: string;
  status: 'processing' | 'completed' | 'failed';
  totalBookmarks: number;
  message?: string;
}

export interface WorkspaceResponse {
  workspace: UserWorkspace;
  bookmarks: BookmarkCache[];
  categories: UserCategory[];
}

// 保存请求类型
export interface SaveRequest {
  bookmarks: {
    id: string;
    finalCategory: string;
    status: 'confirmed' | 'rejected';
  }[];
  categories: {
    id?: string;
    name: string;
    type: 'manual' | 'ai' | 'alphabetical';
    color?: string;
    description?: string;
  }[];
  userConfirmed: boolean;
}

export interface SaveResponse {
  success: boolean;
  message: string;
  resultFileUrl?: string;
}

// 前端状态管理类型
export interface LoadingState {
  isUploading: boolean;
  isParsing: boolean;
  isClassifying: boolean;
  isSaving: boolean;
  isExporting: boolean;
  progress?: number;
  message?: string;
}

export interface UndoRedoState {
  history: BookmarkState[];
  currentIndex: number;
  maxHistorySize: number;
  hasUnsavedChanges: boolean;
}

export interface BookmarkState {
  bookmarks: BookmarkCache[];
  categories: UserCategory[];
  timestamp: number;
}
