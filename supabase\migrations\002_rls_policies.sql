-- Row Level Security (RLS) 策略配置
-- 确保用户只能访问自己的数据

-- 启用 RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_bookmark_workspace ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookmark_processing_cache ENABLE ROW LEVEL SECURITY;

-- Profiles 表的 RLS 策略
-- 用户只能查看和更新自己的 profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- User Bookmark Workspace 表的 RLS 策略
-- 用户只能访问自己的工作空间
CREATE POLICY "Users can view own workspace" ON user_bookmark_workspace
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own workspace" ON user_bookmark_workspace
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own workspace" ON user_bookmark_workspace
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own workspace" ON user_bookmark_workspace
    FOR DELETE USING (auth.uid() = user_id);

-- User Categories 表的 RLS 策略
-- 用户只能访问自己的分类
CREATE POLICY "Users can view own categories" ON user_categories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own categories" ON user_categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own categories" ON user_categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own categories" ON user_categories
    FOR DELETE USING (auth.uid() = user_id);

-- Bookmark Processing Cache 表的 RLS 策略
-- 用户只能访问自己工作空间的书签缓存
CREATE POLICY "Users can view own bookmark cache" ON bookmark_processing_cache
    FOR SELECT USING (
        workspace_id IN (
            SELECT id FROM user_bookmark_workspace 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own bookmark cache" ON bookmark_processing_cache
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT id FROM user_bookmark_workspace 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own bookmark cache" ON bookmark_processing_cache
    FOR UPDATE USING (
        workspace_id IN (
            SELECT id FROM user_bookmark_workspace 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own bookmark cache" ON bookmark_processing_cache
    FOR DELETE USING (
        workspace_id IN (
            SELECT id FROM user_bookmark_workspace 
            WHERE user_id = auth.uid()
        )
    );

-- 创建用于新用户注册的函数
-- 当新用户注册时，自动创建 profile 和默认工作空间
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, display_name)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'display_name');
    
    INSERT INTO user_bookmark_workspace (user_id)
    VALUES (NEW.id);
    
    -- 创建默认分类
    INSERT INTO user_categories (user_id, name, type, color, is_default, sort_order)
    VALUES 
        (NEW.id, '未分类', 'manual', '#6b7280', true, 0),
        (NEW.id, '收件箱', 'manual', '#3b82f6', true, 1);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器，在用户注册时自动执行
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
