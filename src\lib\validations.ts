// 数据验证工具函数

import { FILE_UPLOAD } from './constants';

export function validateFile(file: File): { isValid: boolean; error?: string } {
  // 检查文件大小
  if (file.size > FILE_UPLOAD.maxSize) {
    return {
      isValid: false,
      error: `文件大小超过限制 (${FILE_UPLOAD.maxSize / 1024 / 1024}MB)`
    };
  }

  // 检查文件类型
  const fileExtension = '.' + (file.name.split('.').pop()?.toLowerCase() ?? '');
  if (!FILE_UPLOAD.acceptedTypes.includes(fileExtension as '.html')) {
    return {
      isValid: false,
      error: `不支持的文件类型，仅支持: ${FILE_UPLOAD.acceptedTypes.join(', ')}`
    };
  }

  // 检查MIME类型
  if (!FILE_UPLOAD.acceptedMimeTypes.includes(file.type as 'text/html')) {
    return {
      isValid: false,
      error: '文件格式不正确，请上传HTML书签文件'
    };
  }

  return { isValid: true };
}

export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function validateCategoryName(name: string): { isValid: boolean; error?: string } {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: '分类名称不能为空' };
  }

  if (name.length > 50) {
    return { isValid: false, error: '分类名称不能超过50个字符' };
  }

  // 检查特殊字符
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(name)) {
    return { isValid: false, error: '分类名称包含无效字符' };
  }

  return { isValid: true };
}

export function sanitizeHtml(html: string): string {
  // 基础的HTML清理，移除潜在的危险标签
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '');
}
